<!--
 * @Author: CaiXiaomin
 * @Date: 2025-07-28 11:57:27
 * @LastEditors: CaiXiaomin
 * @LastEditTime: 2025-08-01 15:21:24
 * @FilePath: \platform-face-web\src\views\library\components\ImageUpload.vue
 * @Description: 基于CommonUpload组件封装的图片上传组件
 * 
 * 
-->
<template>
    <div class="upload-area">
        <CommonUpload ref="commonUploadRef" v-bind="$attrs" type="image" :action="uploadUrl()"
            :headers="uploadImageHeaders" :onChange="onChange">
            <template #default>
                <div class="upload-card" v-if="!isSingleImageUploaded">
                    <SvgIcon iconClass="comparison-image-upload" size="74px"></SvgIcon>
                    <span>请点击上传图片</span>
                </div>
            </template>
        </CommonUpload>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { DEFAULT_CONFIG } from '@/constants';

import CommonUpload from '@/components/Common/CommonUpload/index.vue'

// 图片上传相关参数
const { uploadImageAPI, uploadImageHeaders } = DEFAULT_CONFIG || {};

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue', 'onChange'])

const commonUploadRef = ref(null)

// 判断是否为单图上传且已上传
const isSingleImageUploaded = computed(() =>
    !props.multiple && props.modelValue && props.modelValue.length === 1
)

const uploadUrl = () => {
    // const userId = auth.getUserInfo().id;
    const queryParams = {
        owner: 'test'
    };
    const url = `${uploadImageAPI}?${new URLSearchParams(queryParams).toString()}`;
    return url;
};

// 图片上传回调
const onChange = (info) => {
    try {
        // console.log('返回的图片信息', info);
        if (info.status === 'success') {
            const imageId = info.response?.imageId;
            emit('onChange', imageId);
        }
    } catch (error) {
        console.error('返回图片结果错误', error)
    }

};

</script>

<style lang="scss" scoped>
.upload-area {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: url('@/assets/svg/comparison/upload_bg.svg');
    background-origin: content-box;
    background-clip: content-box;
}

.upload-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;

    span {
        font-size: 14px;
        color: #F1F6F8;
    }
}
</style>