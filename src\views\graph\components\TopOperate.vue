<template>
  <div>
    <div class="button-group">
      <slot></slot>
      <a-button @click="openInputDialog">AI输入框模块</a-button>
      <a-button @click="openBilateralDialog">查看双边组件示例</a-button>
      <a-button @click="openPeopleChartDialog">查看人物关系图示例</a-button>
    </div>
    <!-- InputDialog 组件展示弹窗 -->
    <a-modal v-model:open="inputDialogVisible" :title="inputDialogTitle" width="1600px" :mask-closable="true"
      :closable="true" ok-text="确定" cancel-text="取消" @ok="handleInputDialogOk" @cancel="handleInputDialogCancel">
      <AiInputArea />
    </a-modal>
    <a-modal v-model:open="bilateralDialogVisible" title="双边组件示例" width="1600px">
      <BilateralContentExample />
    </a-modal>
    <!-- 人物关系图弹窗 -->
    <a-modal v-model:open="peopleChartDialogVisible" title="人物关系图示例" width="1600px" :footer="null">
      <div style="height: 80vh;">
        <PeopleChart />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import AiInputArea from './AiInputArea.vue';
import BilateralContentExample from './BilateralContentExample.vue';
import PeopleChart from './PeopleChart.vue';

// 输入框弹窗相关
const inputDialogVisible = ref(false)
const bilateralDialogVisible = ref(false)
const peopleChartDialogVisible = ref(false)
const inputDialogTitle = ref('InputDialog 组件展示')

const openInputDialog = () => {
  inputDialogTitle.value = 'InputDialog 组件展示'
  inputDialogVisible.value = true
}
const openBilateralDialog = () => {
  bilateralDialogVisible.value = true
}
const openPeopleChartDialog = () => {
  peopleChartDialogVisible.value = true
}
const handleInputDialogOk = () => {
  message.success('操作成功')
  inputDialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.button-group {
  display: flex;
  padding: 10px;
  gap: 8px;
  flex-wrap: wrap;

  .ant-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.3);
    }
  }
}
</style>