<template>
  <div class="people-chart">
    <div class="chart-header">
      <button class="refresh-btn" @click="refreshGraph">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M23 4v6h-6"/>
          <path d="M1 20v-6h6"/>
          <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
        </svg>
        刷新
      </button>
    </div>
    <div id="people-chart-container" ref="chartContainer"></div>
    
    <!-- 悬浮框 -->
    <div v-if="showTooltip" 
         class="tooltip" 
         :style="{ left: tooltipPosition.x + 'px', top: tooltipPosition.y + 'px' }"
         @click.stop>
      <div class="tooltip-header">
        <span class="tooltip-title">{{ tooltipData.label }}</span>
        <button class="tooltip-close" @click="hideTooltip">×</button>
      </div>
      <div class="tooltip-content">
        <p>
          <strong>用户ID:</strong>
          <span>{{ tooltipData.id }}</span>
        </p>
        <p>
          <strong>关系状态:</strong>
          <span>{{ tooltipData.relationship }}</span>
        </p>
        <p>
          <strong>图片地址:</strong>
          <span>{{ tooltipData.img }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { Graph } from '@antv/g6';

const chartContainer = ref(null);
let graph = null;

// 悬浮框相关数据
const showTooltip = ref(false);
const tooltipPosition = ref({ x: 0, y: 0 });
const tooltipData = ref({});

// 显示悬浮框
const showTooltipHandler = (id, label, relationship, img, event) => {
  
  // 先移除之前的事件监听器
  document.removeEventListener('click', handleOutsideClick);
  
  tooltipData.value = { id, label, relationship, img };
  tooltipPosition.value = {
    x: event.clientX + 10,
    y: event.clientY - 10
  };
  showTooltip.value = true;
  
  // 添加新的点击外部关闭事件
  setTimeout(() => {
    document.addEventListener('click', handleOutsideClick);
    console.log('Event listener added');
  }, 0);
};

// 隐藏悬浮框
const hideTooltip = () => {
  showTooltip.value = false;
  document.removeEventListener('click', handleOutsideClick);
};

// 处理点击外部关闭悬浮框
const handleOutsideClick = (event) => {
  const tooltip = document.querySelector('.tooltip');
  if (tooltip && !tooltip.contains(event.target)) {
    hideTooltip();
  }
};

// 图表数据 - 添加图片信息和关系状态
const data = {
  nodes: new Array(10).fill(0).map((_, i) => ({
    id: `${i}`,
    label: `用户${i}`,
    img: getNodeImage(i),
    relationship: getRelationship(i) // 添加关系状态
  })),
  edges: [
    { source: '0', target: '1', relationship: 'close' },
    { source: '0', target: '2', relationship: 'distant' },
    { source: '0', target: '3', relationship: 'close' },
    { source: '0', target: '4', relationship: 'distant' },
    { source: '0', target: '5', relationship: 'close' },
    { source: '0', target: '6', relationship: 'distant' },
    { source: '0', target: '7', relationship: 'close' },
    { source: '0', target: '8', relationship: 'distant' },
    { source: '0', target: '9', relationship: 'close' },
  ],
};

// 获取节点图片的函数
function getNodeImage(index) {
  const images = [
    'https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png',
    'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
    'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png',
    'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png',
    'https://gw.alipayobjects.com/zos/rmsportal/kZzEzemZyKLKFsojXItE.png',
    'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
    'https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png',
    'https://gw.alipayobjects.com/zos/rmsportal/jZUIxmJycoymBprLOUbT.png',
    'https://gw.alipayobjects.com/zos/rmsportal/psOgztMplJMGpVEqfcgF.png',
    'https://gw.alipayobjects.com/zos/rmsportal/psOgztMplJMGpVEqfcgF.png',
  ];
  return images[index % images.length];
}

// 获取关系状态的函数
function getRelationship(index) {
  // 根据索引返回不同的关系状态
  const relationships = ['close', 'distant', 'close', 'distant', 'close', 'distant', 'close', 'distant', 'close', 'distant'];
  return relationships[index] || 'distant';
}

// 初始化图表
const initGraph = () => {
  if (!chartContainer.value) return;

  const width = chartContainer.value.clientWidth || 800;
  const height = chartContainer.value.clientHeight || 600;

  graph = new Graph({
    container: chartContainer.value,
    width,
    height,
    data,
    node: {
      type: 'html', // 使用 HTML 节点类型，支持自定义 HTML 内容
      style: {
        width: 90,  // 节点容器宽度
        height: 90, // 节点容器高度
        size: [70, 70],
        innerHTML: (d) => `
          <div style="
            width: 90px;
            height: 90px;
            border-radius: 50%;
            border: 1px solid #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #333;
            transform: translate(-10px, -8px);
            position: relative;
          ">
            <img 
              src="${d.img}" 
              alt="${d.label}" 
              style="
                width: 85%; 
                height: 85%; 
                object-fit: cover;
                border-radius: 50%;
                -webkit-user-drag: none;
                -moz-user-drag: none;
                -ms-user-drag: none;
                user-drag: none;
                user-select: none;
              "
              onerror="this.style.display='none'; this.parentElement.style.background='#1890ff';"
            />
            <div style="
              position: absolute;
              bottom: -10px;
              right: -25px;
              display: flex;
              align-items: center;
              gap: 4px;
              color: #fff;
              font-size: 12px;
              font-weight: 500;
              white-space: nowrap;
              cursor: pointer;
              user-select: none;
            " onclick="window.showTooltipHandler && window.showTooltipHandler('${d.id}', '${d.label}', '${d.relationship}', '${d.img}', event)">
              <span style="
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 1px solid #fff;
                display: inline-block;
              "></span>
              ${d.label}
            </div>
          </div>
        `,
        // 移除原来的标签配置，因为已经用HTML自定义了标签
      },
    },
    edge: {
      style: {
        stroke: (d) => {
          // 根据边的关系状态动态调整颜色
          const relationship = d.relationship || 'distant';
          console.log(d, 22222222)
          switch (relationship) {
            case 'close':
              return '#52c41a'; // 关系亲近，使用绿色
            case 'distant':
              return '#B4C3DD'; // 关系疏远，使用浅蓝色
            default:
              return '#B4C3DD'; // 默认颜色
          }
        },
        lineWidth: (d) => {
          // 根据边的关系状态动态调整线条粗细
          const relationship = d.relationship || 'distant';
          switch (relationship) {
            case 'close':
              return 3; // 关系亲近，线条更粗
            case 'distant':
              return 1; // 关系疏远，线条更细
            default:
              return 1; // 默认粗细
          }
        },
      },
    },
    layout: {
      type: 'force', // 使用 force 布局
      linkDistance: (d) => {
        // 在 G6 5.x 中，d 参数可能不是边数据，我们需要从边的数据中获取关系状态
        // 尝试从边的数据中获取关系状态
        const row = data.edges.find(edge => edge.target === d.target);
        const relationship = row?.relationship || 'distant';
        switch (relationship) {
          case 'close':
            return 150; // 关系亲近，距离很近
          case 'distant':
            return 270; // 关系疏远，距离很远
          default:
            return 150; // 默认距离
        }
      },
      collide: {
        radius: 120,    // 碰撞检测半径，防止节点重叠
      },
      // nodeStrength: -100,    // 节点间斥力强度，负值表示斥力
      alpha: 0.3,           // 初始温度参数，控制布局动画强度
      alphaDecay: 0.028,    // 温度衰减率，控制动画持续时间
      alphaMin: 0.0001,      // 最小温度，低于此值停止动画
      velocityDecay: 0.01,  // 速度衰减，控制节点运动阻力
    },
    behaviors: [
      'drag-element', // 拖拽节点行为
      'zoom-canvas', // 画布缩放行为
      {
        type: 'click-select',       // 点击选择行为
        key: 'click-select-1',      // 行为唯一标识
        degree: 2,                  // 选中扩散范围，2表示选中节点及其2度邻居
        state: 'active',            // 选中节点的状态名称
        neighborState: 'neighborActive', // 相邻节点的状态名称
        unselectedState: 'inactive',     // 未选中节点的状态名称
      },
      'drag-canvas', // 画布拖拽行为
    ],
  });

  graph.render();
};

// 处理窗口大小变化
const handleResize = () => {
  if (graph && chartContainer.value) {
    const width = chartContainer.value.clientWidth;
    const height = chartContainer.value.clientHeight;
    graph.resize(width, height);
  }
};

// 刷新图表
const refreshGraph = () => {
  if (graph) {
    graph.destroy();
    graph = null;
  }
  initGraph();
};

// 组件挂载时初始化图表
onMounted(() => {
  // 将方法挂载到window对象，供HTML中的onclick调用
  window.showTooltipHandler = showTooltipHandler;
  
  initGraph();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时销毁图表
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  // 清理window对象和事件监听器
  delete window.showTooltipHandler;
  document.removeEventListener('click', handleOutsideClick);
  if (graph) {
    graph.destroy();
    graph = null;
  }
});
</script>

<style lang="scss" scoped>
.people-chart {
  width: 100%;
  height: 100%;
  background: #333;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .chart-header {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    background-color: #fff;
    border-bottom: 1px solid #eee;

    .refresh-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      background-color: #1890ff;
      color: #fff;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        background-color: #40a9ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
      }

      svg {
        fill: none;
        stroke: currentColor;
        stroke-width: 2;
        transition: transform 0.3s ease;
      }

      &:hover svg {
        transform: rotate(180deg);
      }
    }
  }

  #people-chart-container {
    flex: 1;
    width: 100%;
    min-height: 400px;
  }

  .tooltip {
    position: fixed;
    z-index: 9999;
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
    min-width: 240px;
    max-width: 320px;
    font-size: 14px;
    line-height: 1.6;
    pointer-events: auto;
    backdrop-filter: blur(8px);
    animation: tooltipFadeIn 0.2s ease-out;

    .tooltip-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

      .tooltip-title {
        font-weight: 600;
        color: #2c3e50;
        font-size: 16px;
      }

      .tooltip-close {
        background: none;
        border: none;
        font-size: 20px;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #495057;
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }

    .tooltip-content {
      padding: 16px 20px;

      p {
        margin: 0 0 12px 0;
        color: #6c757d;
        display: flex;
        align-items: center;
        gap: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #2c3e50;
          min-width: 80px;
          font-weight: 600;
        }

        span {
          color: #495057;
          word-break: break-all;
        }
      }
    }
  }

  @keyframes tooltipFadeIn {
    from {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
</style>