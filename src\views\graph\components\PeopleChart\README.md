# PeopleChart 组件

这是一个基于 G6 的图片关系图组件，用于展示带有图片的节点之间的关系，支持根据关系状态动态调整连接距离和样式。

## 功能特性

- 使用 G6 5.x 版本
- 支持 HTML 图片节点显示
- 使用 img 标签显示图片
- 圆形图片裁剪效果
- 支持 d3-force 布局
- 根据关系状态动态调整连接距离
- 根据关系状态动态调整连接样式
- 节点可拖拽
- 支持画布缩放和平移
- 响应式设计，自动适应容器大小
- 动态添加和删除节点
- 图片加载失败时的错误处理

## 使用方法

```vue
<template>
  <div class="chart-container">
    <PeopleChart />
  </div>
</template>

<script setup>
import PeopleChart from './components/PeopleChart.vue';
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 500px;
}
</style>

## 数据结构

组件内部定义了以下数据结构：

```javascript
const data = {
  nodes: new Array(10).fill(0).map((_, i) => ({ 
    id: `${i}`, 
    label: `用户${i}`,
    img: getNodeImage(i), // 图片 URL
    relationship: getRelationship(i) // 关系状态
  })),
  edges: [
    { source: '0', target: '1', relationship: 'close' },
    { source: '0', target: '2', relationship: 'distant' },
    // ... 更多边
  ],
};
```

## 关系状态配置

### 关系类型
- `close`: 关系亲近 - 距离更近(120px)，连接强度更高(5)，线条更粗(2px)，颜色为绿色
- `distant`: 关系疏远 - 距离更远(300px)，连接强度更低(1)，线条更细(1px)，颜色为浅蓝色

### 动态调整规则
- **距离调整**: 亲近关系节点距离120px，疏远关系节点距离300px
- **连接强度**: 亲近关系强度5，疏远关系强度1
- **线条样式**: 亲近关系绿色粗线，疏远关系浅蓝色细线

## 配置说明

### 节点样式
- HTML 节点，尺寸 150x150px
- 圆形图片显示
- 蓝色边框 (#1890ff)
- 白色标签背景
- 可拖拽

### 图片配置
- 使用 img 标签显示图片
- src 来自节点数据的 img 属性
- 圆形裁剪效果
- 图片加载失败时显示蓝色背景
- 自动适应图片尺寸

### 边样式
- 根据关系状态动态调整颜色和粗细
- 亲近关系：绿色，2px 粗细
- 疏远关系：浅蓝色，1px 粗细

### 布局配置
- 使用 d3-force 布局
- 根据关系状态动态调整节点间距离
- 根据关系状态动态调整连接强度
- 碰撞检测半径：80px
- 支持节点拖拽固定

### 交互行为
- 节点拖拽
- 画布缩放
- 画布平移
- 动态添加节点
- 清空所有节点

## 自定义关系状态

您可以通过修改 `getRelationship` 函数来自定义关系状态：

```javascript
function getRelationship(index) {
  const relationships = ['close', 'distant', 'close', 'distant', 'close'];
  return relationships[index] || 'distant';
}
```

## 节点数据结构

每个节点包含以下属性：
- `id`: 节点唯一标识
- `label`: 节点标签文本
- `img`: 图片 URL 地址
- `relationship`: 关系状态 ('close' | 'distant')

## 边数据结构

每条边包含以下属性：
- `source`: 起始节点 ID
- `target`: 目标节点 ID
- `relationship`: 关系状态，影响连接距离和样式

## 依赖

- @antv/g6: ^5.0.49
- Vue 3.x

## 注意事项

1. 确保容器有明确的高度，否则图表可能无法正常显示
2. 组件会自动处理窗口大小变化
3. 组件卸载时会自动清理资源
4. 图片 URL 需要是有效的地址
5. 建议使用正方形图片以获得最佳显示效果
6. 图片加载失败时会显示蓝色背景
7. 关系状态会影响节点的布局位置和连接样式 